"use client";

import React, { useState } from "react";
import Sidebar from "@/components/layout/Sidebar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ProviderManagement from "@/components/providers/ProviderManagement";
import ProviderUsageAnalytics from "@/components/providers/ProviderUsageAnalytics";
import ProviderHealthMonitoring from "@/components/providers/ProviderHealthMonitoring";
import ProviderSelection from "@/components/providers/ProviderSelection";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import ProviderForm from "@/components/providers/ProviderForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function ProvidersPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleProviderCreated = () => {
    setIsCreateDialogOpen(false);
    setRefreshTrigger((prev) => prev + 1);
  };

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />

      <div className="flex-1 overflow-auto">
        <div className="h-full">
          <div className="border-b bg-background px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">
                  AI Providers
                </h1>
                <p className="text-muted-foreground mt-1">
                  Manage and monitor your AI provider configurations
                </p>
              </div>
              <Dialog
                open={isCreateDialogOpen}
                onOpenChange={setIsCreateDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Provider
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Add New AI Provider</DialogTitle>
                  </DialogHeader>
                  <ProviderForm onSuccess={handleProviderCreated} />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <div className="flex-1">
            <Tabs defaultValue="management" className="h-full flex flex-col">
              <div className="border-b px-6">
                <TabsList className="grid w-full max-w-2xl grid-cols-4">
                  <TabsTrigger value="management">Management</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                  <TabsTrigger value="health">Health</TabsTrigger>
                  <TabsTrigger value="selection">Selection</TabsTrigger>
                </TabsList>
              </div>

              <div className="flex-1 p-6">
                <TabsContent value="management" className="h-full mt-0">
                  <ProviderManagement key={refreshTrigger} />
                </TabsContent>

                <TabsContent value="analytics" className="h-full mt-0">
                  <ProviderUsageAnalytics />
                </TabsContent>

                <TabsContent value="health" className="h-full mt-0">
                  <ProviderHealthMonitoring />
                </TabsContent>

                <TabsContent value="selection" className="h-full mt-0">
                  <ProviderSelection />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
