"use client";

import React, { useState } from "react";
import { Provider } from "../../../shared/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, XCircle, AlertCircle, Play, Loader2 } from "lucide-react";
import { apiClient } from "@/lib/api";

interface ProviderTestingProps {
  provider: Provider;
}

interface TestResult {
  status: string;
  result: any;
  error?: string;
  duration: number;
  timestamp: string;
}

export default function ProviderTesting({ provider }: ProviderTestingProps) {
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [testHistory, setTestHistory] = useState<TestResult[]>([]);

  const runTest = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      const response = await apiClient.testProvider(provider.id);

      if (response.success) {
        const result = response.data as TestResult;
        setTestResult(result);
        setTestHistory((prev) => [result, ...prev.slice(0, 4)]); // Keep last 5 tests
      } else {
        const errorResult: TestResult = {
          status: "failed",
          result: null,
          error: response.error || "Test failed",
          duration: 0,
          timestamp: new Date().toISOString(),
        };
        setTestResult(errorResult);
        setTestHistory((prev) => [errorResult, ...prev.slice(0, 4)]);
      }
    } catch (err) {
      const errorResult: TestResult = {
        status: "failed",
        result: null,
        error: err instanceof Error ? err.message : "Test failed",
        duration: 0,
        timestamp: new Date().toISOString(),
      };
      setTestResult(errorResult);
      setTestHistory((prev) => [errorResult, ...prev.slice(0, 4)]);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "failed":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: "default" as const,
      failed: "destructive" as const,
      warning: "outline" as const,
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) {
      return `${duration}ms`;
    }
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Test {provider.name}</h3>
          <p className="text-sm text-muted-foreground">
            Validate API connection and check model availability
          </p>
        </div>
        <Button onClick={runTest} disabled={testing}>
          {testing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Run Test
            </>
          )}
        </Button>
      </div>

      {testing && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin" />
              <div className="flex-1">
                <div className="text-sm font-medium">
                  Testing provider connection...
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  This may take a few seconds
                </div>
              </div>
            </div>
            <Progress value={undefined} className="mt-3" />
          </CardContent>
        </Card>
      )}

      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(testResult.status)}
              Test Result
              {getStatusBadge(testResult.status)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium">Status</label>
                <div className="mt-1">{getStatusBadge(testResult.status)}</div>
              </div>
              <div>
                <label className="text-sm font-medium">Duration</label>
                <div className="mt-1 text-sm">
                  {formatDuration(testResult.duration)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Timestamp</label>
                <div className="mt-1 text-sm text-muted-foreground">
                  {formatTimestamp(testResult.timestamp)}
                </div>
              </div>
            </div>

            {testResult.error && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{testResult.error}</AlertDescription>
              </Alert>
            )}

            {testResult.result && (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">Response</label>
                  <div className="mt-1 p-3 bg-muted rounded-lg">
                    <div className="text-sm">
                      <div className="font-medium text-green-600">
                        {testResult.result.message}
                      </div>
                      {testResult.result.models && (
                        <div className="mt-2">
                          <div className="text-xs font-medium mb-1">
                            Available Models:
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {testResult.result.models
                              .slice(0, 10)
                              .map((model: string) => (
                                <Badge
                                  key={model}
                                  variant="outline"
                                  className="text-xs"
                                >
                                  {model}
                                </Badge>
                              ))}
                            {testResult.result.models.length > 10 && (
                              <Badge variant="outline" className="text-xs">
                                +{testResult.result.models.length - 10} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                      {testResult.result.latency && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          API Latency:{" "}
                          {formatDuration(testResult.result.latency)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {testHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testHistory.map((test, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <div className="text-sm font-medium">
                        {test.status === "success"
                          ? "Test Passed"
                          : "Test Failed"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatTimestamp(test.timestamp)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm">
                      {formatDuration(test.duration)}
                    </div>
                    {test.error && (
                      <div className="text-xs text-red-500 max-w-48 truncate">
                        {test.error}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Test Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <div>
                <div className="font-medium">Connection Test</div>
                <div className="text-muted-foreground">
                  Validates API key and endpoint connectivity
                </div>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <div>
                <div className="font-medium">Model Availability</div>
                <div className="text-muted-foreground">
                  Checks which models are accessible with your API key
                </div>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <div>
                <div className="font-medium">Latency Measurement</div>
                <div className="text-muted-foreground">
                  Measures response time for performance monitoring
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
