import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from "@nestjs/common";
import { ToolsService } from "./tools.service";
import { CreateToolDto } from "./dto/create-tool.dto";
import { UpdateToolDto } from "./dto/update-tool.dto";
import { QueryToolsDto } from "./dto/query-tools.dto";
import { ExecuteToolDto } from "./dto/execute-tool.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ApiResponse } from "../../../shared/types";

@Controller("api/v1/tools")
@UseGuards(JwtAuthGuard)
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Post()
  async create(
    @Body() createToolDto: CreateToolDto,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const tool = await this.toolsService.create(
        createToolDto,
        req.user.id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: tool,
        message: "Tool created successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get()
  async findAll(@Query() query: QueryToolsDto, @Request() req) {
    try {
      const result = await this.toolsService.findAll(
        query,
        req.user.id,
        req.user.organizationId,
        req.user.role,
      );
      return {
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get("stats")
  async getStats(@Request() req): Promise<ApiResponse> {
    try {
      const stats = await this.toolsService.getStats(req.user.organizationId);
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get(":id")
  async findOne(@Param("id") id: string, @Request() req): Promise<ApiResponse> {
    try {
      const tool = await this.toolsService.findOne(
        id,
        req.user.id,
        req.user.organizationId,
        req.user.role,
      );
      return {
        success: true,
        data: tool,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Patch(":id")
  async update(
    @Param("id") id: string,
    @Body() updateToolDto: UpdateToolDto,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const tool = await this.toolsService.update(
        id,
        updateToolDto,
        req.user.id,
        req.user.organizationId,
        req.user.role,
      );
      return {
        success: true,
        data: tool,
        message: "Tool updated successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Delete(":id")
  async remove(@Param("id") id: string, @Request() req): Promise<ApiResponse> {
    try {
      await this.toolsService.remove(
        id,
        req.user.id,
        req.user.organizationId,
        req.user.role,
      );
      return {
        success: true,
        message: "Tool deleted successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post(":id/execute")
  async execute(
    @Param("id") id: string,
    @Body() executeToolDto: ExecuteToolDto,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const result = await this.toolsService.execute(
        id,
        executeToolDto,
        req.user.id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: result,
        message: "Tool executed successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get(":id/executions")
  async getExecutions(
    @Param("id") id: string,
    @Query("page") page: number = 1,
    @Query("limit") limit: number = 10,
    @Request() req,
  ) {
    try {
      const result = await this.toolsService.getExecutions(
        id,
        page,
        limit,
        req.user.id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post(":id/duplicate")
  async duplicate(
    @Param("id") id: string,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const tool = await this.toolsService.duplicate(
        id,
        req.user.id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: tool,
        message: "Tool duplicated successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
